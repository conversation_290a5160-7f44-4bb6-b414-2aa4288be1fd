{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 45648, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 45648, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 45648, "tid": 383, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 45648, "tid": 383, "ts": 1756862561031253, "dur": 506, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 45648, "tid": 383, "ts": 1756862561035049, "dur": 808, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 45648, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 45648, "tid": 1, "ts": 1756862560836976, "dur": 3828, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 45648, "tid": 1, "ts": 1756862560840807, "dur": 38393, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 45648, "tid": 1, "ts": 1756862560879210, "dur": 38020, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 45648, "tid": 383, "ts": 1756862561035860, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 45648, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560835433, "dur": 6589, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560842023, "dur": 179606, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560842923, "dur": 2391, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560845318, "dur": 1266, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846587, "dur": 190, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846779, "dur": 10, "ph": "X", "name": "ProcessMessages 20508", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846790, "dur": 30, "ph": "X", "name": "ReadAsync 20508", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846822, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846852, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846879, "dur": 25, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846907, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846939, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846941, "dur": 29, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846973, "dur": 24, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560846999, "dur": 78, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847081, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847111, "dur": 33, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847146, "dur": 30, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847179, "dur": 23, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847203, "dur": 38, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847245, "dur": 34, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847282, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847285, "dur": 52, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847339, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847340, "dur": 37, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847379, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847381, "dur": 37, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847425, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847429, "dur": 56, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847487, "dur": 1, "ph": "X", "name": "ProcessMessages 1353", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847489, "dur": 33, "ph": "X", "name": "ReadAsync 1353", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847525, "dur": 21, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847548, "dur": 20, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847570, "dur": 25, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847598, "dur": 26, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847627, "dur": 22, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847651, "dur": 20, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847673, "dur": 20, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847695, "dur": 26, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847724, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847750, "dur": 24, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847776, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847798, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847818, "dur": 18, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847838, "dur": 26, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847867, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847890, "dur": 21, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847913, "dur": 19, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847934, "dur": 24, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847960, "dur": 18, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560847981, "dur": 20, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848002, "dur": 18, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848024, "dur": 18, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848043, "dur": 22, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848067, "dur": 18, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848087, "dur": 18, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848107, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848130, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848152, "dur": 20, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848173, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848197, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848215, "dur": 22, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848239, "dur": 19, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848260, "dur": 35, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848299, "dur": 27, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848329, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848351, "dur": 18, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848371, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848392, "dur": 20, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848414, "dur": 19, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848435, "dur": 20, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848456, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848481, "dur": 23, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848506, "dur": 26, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848534, "dur": 23, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848562, "dur": 25, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848590, "dur": 19, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848611, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848631, "dur": 20, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848655, "dur": 25, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848683, "dur": 26, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848710, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848713, "dur": 20, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848736, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848757, "dur": 48, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848807, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848827, "dur": 21, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848850, "dur": 24, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848876, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848877, "dur": 26, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848905, "dur": 22, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848930, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848951, "dur": 18, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848971, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560848991, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849012, "dur": 23, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849039, "dur": 23, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849063, "dur": 2, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849065, "dur": 20, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849088, "dur": 20, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849110, "dur": 17, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849129, "dur": 18, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849149, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849170, "dur": 25, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849198, "dur": 20, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849220, "dur": 18, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849240, "dur": 18, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849260, "dur": 18, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849281, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849312, "dur": 20, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849335, "dur": 20, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849357, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849378, "dur": 18, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849399, "dur": 123, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849524, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849545, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849569, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849592, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849613, "dur": 18, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849634, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849654, "dur": 28, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849684, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849705, "dur": 22, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849730, "dur": 25, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849758, "dur": 20, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849780, "dur": 16, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849798, "dur": 20, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849820, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849843, "dur": 19, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849864, "dur": 17, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849883, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849904, "dur": 17, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849923, "dur": 17, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849943, "dur": 27, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849972, "dur": 19, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560849995, "dur": 28, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850026, "dur": 19, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850047, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850069, "dur": 17, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850087, "dur": 19, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850109, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850131, "dur": 25, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850158, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850180, "dur": 20, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850202, "dur": 17, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850221, "dur": 23, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850248, "dur": 29, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850278, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850280, "dur": 24, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850307, "dur": 22, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850331, "dur": 30, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850364, "dur": 29, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850395, "dur": 20, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850417, "dur": 20, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850440, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850458, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850460, "dur": 18, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850480, "dur": 17, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850499, "dur": 24, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850525, "dur": 24, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850551, "dur": 20, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850573, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850596, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850619, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850621, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850645, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850667, "dur": 17, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850687, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850711, "dur": 27, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850742, "dur": 20, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850764, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850786, "dur": 23, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850810, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850812, "dur": 33, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850846, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850848, "dur": 26, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850876, "dur": 19, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850897, "dur": 18, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850918, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850940, "dur": 18, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850960, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560850984, "dur": 25, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851012, "dur": 21, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851036, "dur": 20, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851058, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851079, "dur": 26, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851108, "dur": 20, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851131, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851156, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851181, "dur": 24, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851207, "dur": 19, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851228, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851251, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851272, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851292, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851314, "dur": 19, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851336, "dur": 19, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851357, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851377, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851378, "dur": 19, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851400, "dur": 17, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851419, "dur": 21, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851442, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851463, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851483, "dur": 20, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851505, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851526, "dur": 17, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851546, "dur": 27, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851576, "dur": 24, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851601, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851623, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851643, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851645, "dur": 25, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851672, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851699, "dur": 26, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851727, "dur": 17, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851746, "dur": 18, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851767, "dur": 26, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851795, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851816, "dur": 18, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851837, "dur": 18, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851857, "dur": 19, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851878, "dur": 19, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851899, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851919, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851941, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851962, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560851982, "dur": 22, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852007, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852027, "dur": 19, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852048, "dur": 17, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852067, "dur": 19, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852088, "dur": 18, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852108, "dur": 18, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852128, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852150, "dur": 23, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852175, "dur": 20, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852198, "dur": 20, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852221, "dur": 37, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852261, "dur": 20, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852282, "dur": 24, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852309, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852331, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852352, "dur": 19, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852373, "dur": 18, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852393, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852414, "dur": 19, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852435, "dur": 18, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852455, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852476, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852496, "dur": 19, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852518, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852538, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852558, "dur": 17, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852577, "dur": 18, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852596, "dur": 18, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852617, "dur": 19, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852638, "dur": 19, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852659, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852681, "dur": 18, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852702, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852722, "dur": 17, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852741, "dur": 19, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852762, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852783, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852805, "dur": 23, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852830, "dur": 21, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852854, "dur": 20, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852876, "dur": 18, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852895, "dur": 82, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560852982, "dur": 31, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853014, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853023, "dur": 32, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853056, "dur": 1, "ph": "X", "name": "ProcessMessages 1742", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853058, "dur": 21, "ph": "X", "name": "ReadAsync 1742", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853081, "dur": 24, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853107, "dur": 20, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853129, "dur": 18, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853150, "dur": 23, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853175, "dur": 24, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853201, "dur": 22, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853224, "dur": 22, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853249, "dur": 23, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853274, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853298, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853319, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853341, "dur": 21, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853364, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853388, "dur": 18, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853407, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853408, "dur": 18, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853428, "dur": 22, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853452, "dur": 419, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853877, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560853919, "dur": 275, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854196, "dur": 66, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854266, "dur": 4, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854274, "dur": 55, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854331, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854335, "dur": 50, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854388, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854391, "dur": 60, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854454, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854458, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854490, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854528, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854530, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854590, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854595, "dur": 39, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854637, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854640, "dur": 46, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854689, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854693, "dur": 53, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854749, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854752, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854808, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854812, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854860, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854865, "dur": 59, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854926, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854929, "dur": 55, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854986, "dur": 3, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560854991, "dur": 53, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855047, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855050, "dur": 54, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855107, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855109, "dur": 52, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855164, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855167, "dur": 47, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855217, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855219, "dur": 52, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855274, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855278, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855327, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855330, "dur": 46, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855379, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855383, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855429, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855432, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855475, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855478, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855523, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855526, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855574, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855577, "dur": 43, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855624, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855628, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855677, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560855679, "dur": 477, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560856161, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560856164, "dur": 62, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560856228, "dur": 4, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560856234, "dur": 8933, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560865193, "dur": 87, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560865387, "dur": 4121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869516, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869520, "dur": 78, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869602, "dur": 9, "ph": "X", "name": "ProcessMessages 1888", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869612, "dur": 35, "ph": "X", "name": "ReadAsync 1888", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869650, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869652, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869688, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869691, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869720, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869722, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869747, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869775, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869838, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869885, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869931, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869934, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869965, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560869968, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870009, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870012, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870053, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870082, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870127, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870151, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870177, "dur": 774, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870954, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870988, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560870990, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871044, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871046, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871080, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871082, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871112, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871114, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871179, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871222, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871254, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871256, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871346, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871374, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871534, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871537, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871579, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871582, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871628, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871658, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871660, "dur": 228, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871893, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871932, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871934, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560871999, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872032, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872035, "dur": 86, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872126, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872152, "dur": 317, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872473, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872475, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872521, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872523, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872584, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872587, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872626, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872629, "dur": 85, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872718, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872744, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872747, "dur": 244, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560872995, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560873031, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560873106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560873107, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560873143, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560873146, "dur": 71, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560873220, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560873247, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560873251, "dur": 1032, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560874286, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560874315, "dur": 490, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862560874807, "dur": 130904, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862561005726, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862561005732, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862561005781, "dur": 1511, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862561007294, "dur": 5629, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862561012927, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862561012929, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862561012962, "dur": 409, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756862561013374, "dur": 7692, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 45648, "tid": 383, "ts": 1756862561035869, "dur": 485, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 45648, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 45648, "tid": 8589934592, "ts": 1756862560833271, "dur": 83992, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 45648, "tid": 8589934592, "ts": 1756862560917266, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 45648, "tid": 8589934592, "ts": 1756862560917269, "dur": 1047, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 45648, "tid": 383, "ts": 1756862561036355, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 45648, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 45648, "tid": 4294967296, "ts": 1756862560817445, "dur": 204998, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756862560821435, "dur": 6683, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756862561025635, "dur": 3030, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756862561027516, "dur": 26, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756862561028731, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 45648, "tid": 383, "ts": 1756862561036361, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756862560839687, "dur": 1752, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862560841450, "dur": 384, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862560841918, "dur": 687, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862560843638, "dur": 1113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756862560845741, "dur": 1042, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756862560846853, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756862560842626, "dur": 10821, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862560853455, "dur": 157049, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862561010506, "dur": 360, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862561012847, "dur": 60, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862561012925, "dur": 1827, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756862560842614, "dur": 10853, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560853488, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_0B128FF1BBAAC47E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560854113, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560854191, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_D95410EB09159078.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560854302, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D443F0926FAEB623.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560854399, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_EC805490FEC13931.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560854631, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_0548B6350D93892B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560854704, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560854825, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560855017, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756862560855124, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756862560855272, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756862560855535, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756862560855812, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560857360, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560858386, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560859449, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560860416, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560861723, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560862558, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560863451, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560864614, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560864681, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560865202, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560865294, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560866063, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560866556, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560866680, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560866942, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756862560867793, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560868110, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560868308, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756862560868909, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560869118, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560869356, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756862560869761, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560869934, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560870021, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560870168, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756862560871032, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560871233, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560871354, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756862560871824, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560872032, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560872132, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756862560872419, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560872616, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560872724, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756862560872949, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862560873118, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862560873232, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756862560874908, "dur": 130787, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756862560842662, "dur": 10834, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560853504, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_71DE750A6D09CA7B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862560854143, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_3A57A969F158AF6F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862560854196, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560854333, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560854413, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_47FD15CC38401525.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862560854563, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560854635, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_47FD15CC38401525.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862560854951, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756862560855066, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756862560855268, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756862560855549, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560855812, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560857061, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560858208, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560859349, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560860196, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560861460, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560862296, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560863186, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560864060, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560864870, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560865224, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560865349, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560866098, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560866594, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560866761, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862560867099, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560867559, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756862560868187, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560868447, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560868549, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560868637, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862560868862, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756862560869375, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560869499, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560870009, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560871011, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560871170, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560871584, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560871951, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560872521, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560872600, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862560873076, "dur": 137441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560842727, "dur": 10799, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560853536, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_0A94E029953135AC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862560854347, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_FC8457FE0E2359F5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862560854411, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560854589, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560854643, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_3AD12250A59F4CEE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862560854829, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_3D53C0654B9396F1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862560854895, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560854959, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_3D53C0654B9396F1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862560855257, "dur": 489, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756862560855748, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560855824, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560857079, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560858010, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560859213, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560860056, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560861302, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560862190, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560863109, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560863972, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560864808, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560865229, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560865324, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560866078, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560866570, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560866864, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560867636, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560867833, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560867954, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560868016, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560868073, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560868400, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560868566, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560868692, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560868903, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560869123, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560869237, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560869385, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560869478, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560870046, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560871047, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560871184, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560871600, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560871991, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560872574, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862560873123, "dur": 137430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560843313, "dur": 10486, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560853800, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B3B4C94707F7A218.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862560853959, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560854166, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560854355, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_7CE7E2E85C9B872F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862560854450, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_558CAE984AE1D1A5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862560854898, "dur": 751, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_56F11C16920C2AB4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862560855657, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862560855835, "dur": 9241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756862560865077, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560865321, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560866075, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560866580, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560866835, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560867679, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560867908, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560867997, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560868079, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560868407, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560868576, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560868684, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560868904, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560869093, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560869246, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560869401, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560869476, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560869943, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560870017, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560871049, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560871179, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560871593, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560871997, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560872525, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560872601, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862560873101, "dur": 137439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560842687, "dur": 10818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560853512, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_36F2911304CC020F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862560854180, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560854236, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_AF9E01432509B98A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862560854299, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3A6F5DE8BB04652D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862560854412, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D2ABA6F656B34D47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862560854551, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560854675, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560854768, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560854842, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862560854939, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560855332, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756862560855861, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560857116, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560858084, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560859282, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560860174, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560861364, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560862215, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560863220, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560864173, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560864975, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560865235, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560865328, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560866085, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560866578, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560866825, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560867627, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560867882, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560868065, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560868336, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560868393, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560868555, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560868652, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560868702, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560868921, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560869083, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560869273, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560869469, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560869762, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560869898, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560870038, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560871053, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560871202, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560871626, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560871993, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560872583, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862560873110, "dur": 137439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560842715, "dur": 10799, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560853521, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_448E988461C74DB0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862560854143, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560854303, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F3E222EC1821C3F6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862560854407, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EFBDB924F7F1B25C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862560854590, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_20E9E08310ABADB8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862560854792, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_AF814272D3883F57.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862560854892, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560855030, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756862560855224, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1756862560855572, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756862560855637, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756862560855796, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560856954, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560857966, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560859182, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560860012, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560861272, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560862120, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560862918, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560863743, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560864679, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560865200, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560865296, "dur": 765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560866062, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560866557, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560866677, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862560866827, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560866883, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756862560867734, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560867917, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560868128, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560868320, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560868425, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560868588, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560868686, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560868951, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560869079, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560869227, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560869354, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560869484, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560870054, "dur": 961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560871016, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560871187, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560871629, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560871985, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560872524, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560872603, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862560873080, "dur": 137453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560842747, "dur": 10796, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560853556, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4CE88DA2C6FA0DDC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862560854232, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4CE88DA2C6FA0DDC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862560854286, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_7D055410D6C309E4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862560854396, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D52594234CD03E41.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862560854609, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560854677, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D52594234CD03E41.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862560854788, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560854909, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1756862560855148, "dur": 617, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756862560855807, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560855869, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560856914, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560857762, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560858901, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560859772, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560860637, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560861949, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560862894, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@8f3c30ecd081\\Editor\\UI\\DrawActionToolbar.cs"}}, {"pid": 12345, "tid": 7, "ts": 1756862560862809, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560864334, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560864929, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560865237, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560865334, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560866087, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560866582, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560866812, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560867625, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560867882, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560868075, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560868416, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560868573, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560868730, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560868911, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560869109, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560869247, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560869381, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560869486, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560870039, "dur": 1018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560871058, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560871169, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560871585, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560871640, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560871968, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560872619, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862560873090, "dur": 137439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560842787, "dur": 10785, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560853585, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3352A694B6772687.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756862560854416, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560854554, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_57A4CF732EF2CD29.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756862560854748, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_47D5F37EEAD56BE0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756862560854802, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560854884, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756862560854935, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560855136, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756862560855343, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756862560855514, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1756862560855639, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756862560855728, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560855784, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560856876, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560857783, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560859045, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560859905, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560860727, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560861968, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560862866, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560863837, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560864807, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560865214, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560865311, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560866072, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560866569, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560866874, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560867638, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560867914, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560868093, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560868349, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560868412, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560868569, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560868705, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560868924, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560869086, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560869411, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560869471, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560869759, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560869916, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560870031, "dur": 1003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560871035, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560871184, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560871622, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560871995, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560872623, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862560873094, "dur": 137457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560842810, "dur": 10791, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560853614, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_470099C400F771FA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862560854248, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560854394, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_D7FCB2E6762B3FB1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862560854465, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862560854638, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862560855006, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1756862560855246, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1756862560855316, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756862560855420, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756862560855781, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560856924, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560859179, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\Sidebars\\ISidebarPanelContent.cs"}}, {"pid": 12345, "tid": 9, "ts": 1756862560858301, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560860103, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560861469, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560862343, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560863185, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560864071, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560864940, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560865230, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560865340, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560866094, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560866586, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560866800, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560867627, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560867837, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560867906, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560868002, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560868068, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560868292, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560868378, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560868544, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560868641, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560868706, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560868926, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560869087, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560869240, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560869473, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560869895, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560870040, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560871041, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560871196, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560871616, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560872004, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560872528, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560872597, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862560873071, "dur": 137439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560842832, "dur": 10785, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560853625, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862560854219, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560854279, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862560854409, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560854536, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_6EEC5CB3D74D8DC0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862560854745, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B2740E25E009BD1A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862560854803, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560854964, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B2740E25E009BD1A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862560855308, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756862560855381, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560855440, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1756862560855659, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756862560855760, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560855817, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560856940, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560858159, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560859408, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560860348, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560861670, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560862556, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560863362, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560864305, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560865020, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560865205, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560865332, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560866084, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560866587, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560866786, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862560867201, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756862560867906, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560868073, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560868130, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560868310, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560868390, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560868586, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560868729, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560868931, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560869078, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560869229, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560869409, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560869474, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560869894, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560870042, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560871042, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560871181, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560871599, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560871979, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560872540, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560872590, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862560873068, "dur": 137434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560842867, "dur": 10762, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560853636, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_88B5229BB905F051.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560854300, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5706F0A31B0C16B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560854366, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560854419, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5706F0A31B0C16B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560854491, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_5A56B4C00496EA36.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560854664, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_076091C3DFC2A810.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560854715, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560854794, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560854911, "dur": 649, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756862560855562, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560855728, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560855795, "dur": 9371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756862560865167, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560865364, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560865457, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756862560865949, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560866125, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560866212, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756862560866447, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560866679, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560866827, "dur": 1569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756862560868397, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560868669, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560868835, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560868930, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756862560869926, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560870019, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560870139, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756862560870895, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560871075, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862560871179, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756862560871469, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560871648, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560871949, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560872521, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560872607, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862560873106, "dur": 137441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560842882, "dur": 10758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560853649, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7E5B9059DDF5C58D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862560854406, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_21F715593E655655.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862560854479, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_25538968F9D76790.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862560854716, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3FD29ACDA5717940.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862560854855, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560854908, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9ADCC66C60352D2B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862560855255, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756862560855569, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756862560855850, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560857163, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560858020, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560859153, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560859996, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560861240, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560862115, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560863267, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560864245, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560865026, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560865301, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560866113, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560866597, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560866722, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862560866975, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756862560867767, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560867944, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862560868220, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756862560868626, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560868964, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560869088, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560869242, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560869465, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560869654, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560869933, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560870024, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560871021, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560871194, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560871617, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560871994, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560872586, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862560873099, "dur": 137456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560842939, "dur": 10715, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560853665, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BB73E5A042403DAF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862560854289, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560854432, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_88DF725418823539.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862560854540, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A067B7A9CA1EAF47.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862560854611, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560854668, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A067B7A9CA1EAF47.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862560854741, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_CBCEC9956E2477BD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862560854802, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560854904, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_CBCEC9956E2477BD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862560855022, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756862560855305, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756862560855533, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756862560855736, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560855793, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560856960, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560857968, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560859285, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560860131, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560861379, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560862245, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560863051, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560863988, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560864875, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560865222, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560865347, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560866097, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560866591, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560866773, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862560867000, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560867094, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756862560867722, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560867938, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560868011, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560868069, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560868291, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560868382, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560868543, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560868640, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560868710, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560868929, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560869077, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560869231, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560869514, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560869951, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560870016, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560871052, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560871172, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560871583, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560871950, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560872523, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560872606, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862560873087, "dur": 137457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560842968, "dur": 10698, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560853675, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_9520DE7000879E41.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862560854422, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_9520DE7000879E41.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862560854516, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6941CE050E1CF5A2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862560854864, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862560854963, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862560855598, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756862560855774, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560856916, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560858022, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560859183, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560860052, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560861298, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560862157, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560862985, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560863866, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560864904, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560865226, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560865326, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560866079, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560866572, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560866854, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560867634, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560867925, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560868013, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560868080, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560868385, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560868594, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560868647, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560868704, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560868922, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560869082, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560869228, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560869352, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560869512, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560869960, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560870012, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560871012, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560871214, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560871620, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560871970, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560872577, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560873060, "dur": 45270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560919751, "dur": 215, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 14, "ts": 1756862560918331, "dur": 1641, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862560919972, "dur": 90584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560843002, "dur": 10676, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560853686, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_CEA60A69666C96C6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862560854417, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560854560, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862560854611, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560854665, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862560854846, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560854962, "dur": 330, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756862560855347, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756862560855454, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756862560855658, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756862560855828, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560856931, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560858679, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Subtract.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756862560857850, "dur": 1991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560859842, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560861384, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560862237, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560863080, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560863945, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560864797, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560865208, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560865337, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560866099, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560866593, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560866751, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862560867057, "dur": 1521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756862560868578, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560868725, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862560869056, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756862560869614, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560869749, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560869924, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560870032, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560871033, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560871200, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560871619, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560871955, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560872523, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560872587, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862560873092, "dur": 137444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560843044, "dur": 10651, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560853707, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_05BE74699551E17D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862560854425, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_05BE74699551E17D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862560854574, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560854634, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_886CDD09B49D7AF9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862560854839, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862560855029, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1756862560855127, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756862560855270, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756862560855442, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756862560855636, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560855741, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1756862560855820, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560856927, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560858021, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560859181, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560860001, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560860852, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560862072, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560862888, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560863742, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560864733, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560865211, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560865307, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560866069, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560866565, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560866884, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560867641, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560867897, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560867958, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560868019, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560868077, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560868405, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560868562, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560868687, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560868903, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560869091, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560869241, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560869492, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560870028, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560871039, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560871176, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560871636, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560871950, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560872014, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560872527, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560872599, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862560873072, "dur": 137428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560843087, "dur": 10624, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560853718, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_6A4766F7572387DA.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862560854420, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E57DD11F6513DD1B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862560854676, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1CF8F8636ADD1698.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862560855086, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1756862560855597, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756862560855821, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560856980, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560857981, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560859198, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560860125, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560861451, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560862368, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560863193, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560864029, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560864843, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560865218, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560865332, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560866082, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560866576, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560866845, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560867624, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560867912, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560868072, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560868333, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560868389, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560868553, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560868638, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862560868920, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756862560869512, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560869644, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560869942, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560870017, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560871022, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560871188, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560871606, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560871973, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560872532, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560872593, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862560873063, "dur": 137435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560843148, "dur": 10587, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560853745, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_30460E0C3839B364.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862560854429, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560854563, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F9E2212BE69EFFE3.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862560854841, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756862560855110, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756862560855242, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756862560855422, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756862560855778, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560856879, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560857744, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560858927, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560859784, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560860712, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560861973, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560862868, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560863648, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560864677, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560865201, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560865298, "dur": 766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560866064, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560866595, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560866734, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862560866906, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560866971, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756862560867718, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560867936, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862560868160, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756862560869205, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560869402, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560869477, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560869931, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560870027, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560871029, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560871201, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560871632, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560871972, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560872525, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560872602, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862560873098, "dur": 137440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560843118, "dur": 10604, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560853730, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B40F3C4651EACB98.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756862560854406, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560854567, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B40F3C4651EACB98.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756862560854789, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EF1B9BDC36CA0637.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756862560854897, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756862560855103, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756862560855212, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560855264, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1756862560855530, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1756862560855702, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560855756, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756862560856008, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560857087, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560857950, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560859029, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560859988, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560860860, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560862075, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560862889, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560863739, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560864682, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560865336, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560866090, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560866583, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560866798, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756862560867097, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756862560867898, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560868003, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560868076, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560868401, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560868561, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560868694, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560868917, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560869087, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560869270, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560869360, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560869483, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560870013, "dur": 995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560871009, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560871222, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560871587, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560871948, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560872036, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756862560872158, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756862560872410, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560872589, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862560873075, "dur": 137438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560843186, "dur": 10561, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560853755, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_81ADD57237B7A215.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862560854591, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_128BE6A580A94DC6.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862560854685, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_128BE6A580A94DC6.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862560855141, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560855245, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1756862560855325, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560855386, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560855443, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756862560855645, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756862560855873, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560856983, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560858041, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560859245, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560860092, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560861493, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560862336, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560863242, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560864200, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560864966, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560865231, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560865350, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560866067, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560866599, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560866701, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862560866905, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756862560867764, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560867955, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560868111, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560868340, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560868398, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560868556, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560868699, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560868916, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560869105, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560869250, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560869371, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560869488, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560870057, "dur": 994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560871051, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560871186, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560871604, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560871964, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560872585, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862560873079, "dur": 137453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560843212, "dur": 10548, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560853769, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_C2B2B5EFE7050494.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862560854264, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_4F4FD9FF61AF6F28.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862560854401, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DA76F58CBF310F62.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862560854568, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DA76F58CBF310F62.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862560854722, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E38ED73995C25B9E.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862560854796, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560854967, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756862560855133, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756862560855428, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1756862560855642, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756862560855759, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756862560855816, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560855890, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560857038, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560858034, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560859269, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560860233, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560861516, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560862348, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560863210, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560863832, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560864748, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560865210, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560865304, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560866095, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560866599, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560866711, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862560866958, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560867473, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756862560868156, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560868339, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862560868585, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756862560869112, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560869277, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560869482, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560870023, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560871024, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560871192, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560871613, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560871981, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560872536, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560872589, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862560873062, "dur": 137445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560843245, "dur": 10528, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560853932, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_3BECFE23642E1E2A.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862560854371, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_80D3DD5FCF7A4155.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862560854505, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F270133E58124F18.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862560854832, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862560854935, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1756862560855031, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1756862560855190, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560855249, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1756862560855642, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1756862560855757, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560856023, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560857152, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560858065, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560859468, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560860489, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560861768, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560862670, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560863457, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560864407, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560864727, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560865238, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560865350, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560866104, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560866601, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560866692, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862560866872, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756862560867480, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560867675, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560867900, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560868115, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560868422, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560868577, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560868726, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862560869005, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756862560869615, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560869751, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560869906, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560870036, "dur": 995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560871031, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560871196, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560871616, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560871992, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560872580, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560873061, "dur": 46918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862560919980, "dur": 90580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560843274, "dur": 10512, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560853795, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E4EBE1DEA4B9DBF9.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862560853960, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C25CFDCCBD0E71F0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862560854242, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_798AE90902F00157.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862560854427, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B76ACB483990A7B3.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862560854565, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560854627, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AFDD633454108E81.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862560854678, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560854801, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AFDD633454108E81.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862560855033, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756862560855264, "dur": 366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756862560855632, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756862560855802, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756862560856088, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560857772, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560859083, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560859918, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560860803, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560862069, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560863000, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560863844, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560864753, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560865217, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560865312, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560866075, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560866566, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560866685, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862560866856, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560866915, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756862560867523, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560868062, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862560868307, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756862560868942, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560869119, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560869255, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560869391, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560869479, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560870035, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560871061, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560871191, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560871609, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560871983, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560872603, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862560873081, "dur": 137438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560842648, "dur": 10838, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560853493, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_253848F44BDD5E17.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862560853929, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_3F0948DC6B40CBC0.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862560854421, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560854547, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8065517B5A536D03.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862560854659, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F15DE0412BCD77BB.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862560854818, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862560854916, "dur": 638, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862560855624, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756862560855686, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560855799, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560857149, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560858141, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560859261, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560860090, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560861395, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560862241, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560863124, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560863967, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560864832, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560865215, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560865341, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560866114, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560866561, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560866895, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560867631, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560867922, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560868010, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560868065, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560868294, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560868381, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560868546, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560868640, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862560868859, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1756862560869339, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560869472, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560869893, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560870044, "dur": 999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560871044, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560871177, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560871592, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560871960, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560872584, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862560873086, "dur": 137438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862561018548, "dur": 1996, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 45648, "tid": 383, "ts": 1756862561036798, "dur": 1784, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 45648, "tid": 383, "ts": 1756862561038634, "dur": 1728, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 45648, "tid": 383, "ts": 1756862561033806, "dur": 7349, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}