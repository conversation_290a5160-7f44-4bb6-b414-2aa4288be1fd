{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 45648, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 45648, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 45648, "tid": 394, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 45648, "tid": 394, "ts": 1756862591385348, "dur": 9, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 45648, "tid": 394, "ts": 1756862591385365, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 45648, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 45648, "tid": 1, "ts": 1756862591122562, "dur": 1192, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 45648, "tid": 1, "ts": 1756862591123755, "dur": 32683, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 45648, "tid": 1, "ts": 1756862591156440, "dur": 24629, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 45648, "tid": 394, "ts": 1756862591385371, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 45648, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591122540, "dur": 11638, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591134179, "dur": 250644, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591134185, "dur": 28, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591134215, "dur": 325, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591134549, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591134552, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591134604, "dur": 4, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591134609, "dur": 2717, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591139956, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591139960, "dur": 179, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140141, "dur": 10, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140152, "dur": 66, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140222, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140224, "dur": 56, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140282, "dur": 1, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140284, "dur": 42, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140328, "dur": 38, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140370, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140372, "dur": 56, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140433, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140438, "dur": 76, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140517, "dur": 2, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140521, "dur": 35, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140558, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140560, "dur": 43, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140606, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140609, "dur": 36, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140646, "dur": 2, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140651, "dur": 47, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140700, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140703, "dur": 44, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140748, "dur": 1, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140750, "dur": 38, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140791, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140795, "dur": 50, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140847, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591140851, "dur": 192, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141048, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141086, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141088, "dur": 166, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141257, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141259, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141304, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141306, "dur": 30, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141339, "dur": 23, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141364, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141366, "dur": 43, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141414, "dur": 48, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141467, "dur": 2, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141471, "dur": 49, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141522, "dur": 38, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141563, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141566, "dur": 38, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141606, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141610, "dur": 50, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141662, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141666, "dur": 35, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141702, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141704, "dur": 31, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141737, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141741, "dur": 43, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141785, "dur": 1, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141787, "dur": 51, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141840, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141842, "dur": 31, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141875, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141877, "dur": 28, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141906, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141908, "dur": 28, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141939, "dur": 32, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591141974, "dur": 29, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142006, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142008, "dur": 33, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142043, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142045, "dur": 49, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142096, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142100, "dur": 41, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142144, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142146, "dur": 47, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142196, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142200, "dur": 36, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142237, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142239, "dur": 27, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142268, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142271, "dur": 50, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142324, "dur": 29, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142355, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142356, "dur": 38, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142397, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142400, "dur": 66, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142472, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142522, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142523, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142561, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142562, "dur": 23, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142588, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142616, "dur": 23, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142642, "dur": 22, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142665, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142668, "dur": 27, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142697, "dur": 24, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142722, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142724, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142746, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142748, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142771, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142792, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142795, "dur": 30, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142827, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142828, "dur": 26, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142857, "dur": 25, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142884, "dur": 22, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142908, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142942, "dur": 24, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142968, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142970, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591142998, "dur": 21, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143021, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143046, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143048, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143071, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143093, "dur": 26, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143122, "dur": 23, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143147, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143171, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143173, "dur": 22, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143200, "dur": 29, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143234, "dur": 36, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143273, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143276, "dur": 27, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143306, "dur": 29, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143338, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143361, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143385, "dur": 27, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143415, "dur": 29, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143446, "dur": 27, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143478, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143507, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143509, "dur": 21, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143532, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143555, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143556, "dur": 29, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143588, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143590, "dur": 29, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143620, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143622, "dur": 28, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143653, "dur": 21, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143678, "dur": 25, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143706, "dur": 27, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143736, "dur": 23, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143762, "dur": 23, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143786, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143788, "dur": 19, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143809, "dur": 23, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143836, "dur": 29, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143867, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143868, "dur": 24, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143894, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143897, "dur": 23, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143922, "dur": 20, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143943, "dur": 2, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143946, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143970, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591143973, "dur": 27, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144002, "dur": 47, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144051, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144053, "dur": 35, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144089, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144091, "dur": 23, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144117, "dur": 17, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144135, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144137, "dur": 20, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144159, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144185, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144209, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144211, "dur": 28, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144241, "dur": 25, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144269, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144271, "dur": 27, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144301, "dur": 24, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144327, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144328, "dur": 22, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144353, "dur": 22, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144377, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144379, "dur": 27, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144409, "dur": 21, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144432, "dur": 20, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144455, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144483, "dur": 23, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144507, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144509, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144534, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144556, "dur": 25, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144586, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144613, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144615, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144641, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144665, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144666, "dur": 37, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144705, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144707, "dur": 38, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144746, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144748, "dur": 20, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144771, "dur": 140, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144915, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144946, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144948, "dur": 23, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591144976, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145001, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145003, "dur": 18, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145024, "dur": 12, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145039, "dur": 17, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145060, "dur": 21, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145083, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145107, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145109, "dur": 27, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145138, "dur": 23, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145163, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145164, "dur": 21, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145188, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145210, "dur": 28, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145241, "dur": 23, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145266, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145268, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145295, "dur": 22, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145321, "dur": 19, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145343, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145366, "dur": 30, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145399, "dur": 22, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145424, "dur": 23, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145449, "dur": 19, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145471, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145495, "dur": 24, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145521, "dur": 25, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145549, "dur": 20, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145571, "dur": 22, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145597, "dur": 69, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145668, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145695, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145717, "dur": 21, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145741, "dur": 30, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145774, "dur": 24, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145800, "dur": 21, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145827, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145849, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145875, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145877, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145905, "dur": 22, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145930, "dur": 26, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145957, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145959, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591145984, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146011, "dur": 26, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146040, "dur": 23, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146065, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146066, "dur": 24, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146093, "dur": 27, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146122, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146124, "dur": 26, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146152, "dur": 23, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146179, "dur": 19, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146200, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146202, "dur": 23, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146228, "dur": 29, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146259, "dur": 27, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146290, "dur": 29, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146320, "dur": 7, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146329, "dur": 22, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146353, "dur": 34, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146390, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146392, "dur": 26, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146421, "dur": 23, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146448, "dur": 19, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591146470, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591149831, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591149833, "dur": 170, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150006, "dur": 11, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150018, "dur": 36, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150056, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150058, "dur": 42, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150103, "dur": 39, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150144, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150147, "dur": 38, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150187, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150189, "dur": 38, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150229, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150231, "dur": 31, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150265, "dur": 51, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150319, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150320, "dur": 38, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150361, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150363, "dur": 45, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150411, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150413, "dur": 34, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150451, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150452, "dur": 33, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150486, "dur": 2, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150490, "dur": 36, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150529, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150531, "dur": 40, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150573, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150575, "dur": 43, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150621, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150623, "dur": 34, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150659, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150661, "dur": 37, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150701, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150703, "dur": 34, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150739, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150741, "dur": 41, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150784, "dur": 34, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150819, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150821, "dur": 38, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150862, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150863, "dur": 43, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150909, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150911, "dur": 36, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150949, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591150961, "dur": 41, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151005, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151007, "dur": 43, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151053, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151055, "dur": 39, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151097, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151098, "dur": 50, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151151, "dur": 2, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151155, "dur": 43, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151200, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151201, "dur": 41, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151257, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151259, "dur": 43, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151304, "dur": 1, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151307, "dur": 33, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151342, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151343, "dur": 28, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151374, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151376, "dur": 40, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151419, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151421, "dur": 37, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151461, "dur": 30, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151494, "dur": 174, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151673, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151730, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151733, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151790, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151793, "dur": 54, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151850, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151852, "dur": 39, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151895, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151898, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151941, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151944, "dur": 48, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151996, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591151998, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152058, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152061, "dur": 55, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152119, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152122, "dur": 57, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152182, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152185, "dur": 45, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152233, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152236, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152294, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152298, "dur": 37, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152337, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152339, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152388, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152390, "dur": 48, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152442, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152445, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152488, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152491, "dur": 56, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152550, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152553, "dur": 50, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152607, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152611, "dur": 59, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152673, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152677, "dur": 55, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152735, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152739, "dur": 60, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152802, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152806, "dur": 54, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152863, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152873, "dur": 53, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152930, "dur": 3, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152934, "dur": 44, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152980, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591152982, "dur": 46, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153030, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153033, "dur": 54, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153090, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153093, "dur": 54, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153150, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153152, "dur": 44, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153199, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153201, "dur": 37, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153241, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153244, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153296, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153298, "dur": 53, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153353, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153355, "dur": 46, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153404, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153406, "dur": 44, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153453, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153457, "dur": 46, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153506, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153509, "dur": 39, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153551, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153555, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153599, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153602, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153649, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153652, "dur": 44, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153698, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153700, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153750, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153752, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153808, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153812, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153859, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153861, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153899, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153902, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153937, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153968, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591153970, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591154137, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591154167, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591154169, "dur": 8484, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591162659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591162662, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591162713, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591162715, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591162761, "dur": 289, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163054, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163088, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163146, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163149, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163184, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163186, "dur": 67, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163258, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163286, "dur": 523, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163814, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163849, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163851, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163899, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591163930, "dur": 81, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164014, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164040, "dur": 267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164310, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164347, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164396, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164439, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164441, "dur": 251, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164696, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164733, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164735, "dur": 42, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164780, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591164783, "dur": 548, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165334, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165336, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165377, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165379, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165436, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165470, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165472, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165515, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165517, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165570, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165573, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165618, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165621, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165670, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165672, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165726, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165729, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165776, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165779, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165825, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165827, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165870, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165873, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165913, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165946, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591165948, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166012, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166044, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166067, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166090, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166135, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166138, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166210, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166253, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166256, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166289, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166352, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166390, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166396, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166440, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166482, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166484, "dur": 31, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166518, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166519, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166592, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166634, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166636, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166677, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166679, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166736, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166763, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166827, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166859, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166889, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591166977, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167020, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167022, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167065, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167101, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167102, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167136, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167173, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167212, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167261, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167297, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167299, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167339, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167341, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167383, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167384, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167421, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167458, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167489, "dur": 271, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167764, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167801, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167803, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167861, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167894, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591167895, "dur": 113, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591168012, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591168035, "dur": 806, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591168845, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591168847, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591168893, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591168895, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591168939, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591168977, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591168978, "dur": 83, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591169065, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591169159, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591169186, "dur": 3868, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591173058, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591173060, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591173096, "dur": 3, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591173100, "dur": 135431, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591308542, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591308549, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591308596, "dur": 28, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591308625, "dur": 4737, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591313365, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591313368, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591313408, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591313410, "dur": 1937, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591315351, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591315388, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591315403, "dur": 60789, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591376200, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591376203, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591376247, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591376251, "dur": 1029, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591377285, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591377360, "dur": 41, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591377403, "dur": 710, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591378117, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591378145, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 45648, "tid": 25769803776, "ts": 1756862591378146, "dur": 6671, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 45648, "tid": 394, "ts": 1756862591385379, "dur": 698, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 45648, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 45648, "tid": 21474836480, "ts": 1756862591122510, "dur": 58576, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 45648, "tid": 21474836480, "ts": 1756862591181088, "dur": 43, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 45648, "tid": 394, "ts": 1756862591386078, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 45648, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 45648, "tid": 17179869184, "ts": 1756862591119297, "dur": 265556, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 45648, "tid": 17179869184, "ts": 1756862591119379, "dur": 3095, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 45648, "tid": 17179869184, "ts": 1756862591384856, "dur": 47, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 45648, "tid": 17179869184, "ts": 1756862591384864, "dur": 12, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 45648, "tid": 394, "ts": 1756862591386083, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756862591134860, "dur": 1709, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591136577, "dur": 381, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591137032, "dur": 709, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591138758, "dur": 2073, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_25538968F9D76790.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756862591141487, "dur": 232, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B2740E25E009BD1A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756862591148225, "dur": 2459, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1756862591137765, "dur": 14372, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591152147, "dur": 225891, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591378041, "dur": 156, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591378197, "dur": 211, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591378466, "dur": 70, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591378707, "dur": 54, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591378776, "dur": 1413, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756862591137598, "dur": 14562, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591152177, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_0B128FF1BBAAC47E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862591152422, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B3B4C94707F7A218.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862591152582, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_446E57718A74AE8B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862591152645, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591152753, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_7D055410D6C309E4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862591152831, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591152903, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E57DD11F6513DD1B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862591152955, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591153127, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F15DE0412BCD77BB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862591153305, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1756862591153506, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756862591153674, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591153724, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756862591153808, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756862591153918, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756862591153982, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591154090, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756862591154297, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591154375, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591155452, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591156266, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591157175, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591158044, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591159009, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591160186, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591161028, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591161909, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591162733, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591163363, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591163809, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591164566, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591165097, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756862591165375, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756862591166157, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591166554, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591166928, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591167087, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591167354, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591167731, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591167922, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591168060, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591168486, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591168543, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591169562, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591169730, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1756862591169818, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591170158, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591170559, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591171115, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756862591171644, "dur": 206386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591137629, "dur": 14562, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591152200, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_71DE750A6D09CA7B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862591152392, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591152469, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_3BECFE23642E1E2A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862591152597, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591152765, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_C1731F7A9273BA75.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862591152935, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F270133E58124F18.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862591153082, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591153328, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862591153437, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862591153600, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756862591153703, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591154003, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1756862591154325, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591154396, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591155434, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591156325, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591157347, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591158236, "dur": 1265, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceFieldAccessor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756862591158184, "dur": 2250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591160435, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591161402, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591162260, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591163146, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591163333, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591163766, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591164527, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591165029, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591165089, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756862591165396, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756862591166298, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591166610, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591166829, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591167080, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591167327, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591167722, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591167902, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591168171, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591168542, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591169594, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591169717, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591170148, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591170550, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591171128, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756862591171708, "dur": 206449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591137618, "dur": 14559, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591152186, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_253848F44BDD5E17.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862591152348, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591152419, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_253848F44BDD5E17.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862591152486, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C25CFDCCBD0E71F0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862591152655, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C25CFDCCBD0E71F0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862591152806, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_D7FCB2E6762B3FB1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862591152908, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591153109, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A964C83E710562E0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756862591153377, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756862591153448, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591153500, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756862591153725, "dur": 396, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756862591154140, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591154341, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591155366, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591156246, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591157174, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591158023, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591160424, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@6b9e48457ddb\\Runtime\\Evaluation\\RuntimeClip.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756862591159074, "dur": 1945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591161020, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591161899, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591162784, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591163510, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591163792, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591164551, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591165163, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591166200, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591166307, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591166465, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591166538, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591166825, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591167127, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591167276, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591167738, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591167910, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591168124, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591168518, "dur": 1062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591169580, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591169715, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591170147, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591170557, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591171137, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756862591171647, "dur": 206641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591137670, "dur": 14532, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591152210, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_36F2911304CC020F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862591152425, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0903FEE7BAED8E66.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862591152535, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591152634, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D83BBE6D171D2E8B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862591152903, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_FC8457FE0E2359F5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862591153050, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862591153173, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_AF814272D3883F57.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756862591153328, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1756862591153691, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591153847, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756862591154067, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1756862591154243, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591154299, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591154352, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1756862591154498, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591155536, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591156352, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591157325, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591158180, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591159113, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591160671, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591161604, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591162512, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591163447, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591163785, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591164544, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591165193, "dur": 842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591166035, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591166291, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591166467, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591166576, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591166860, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591167086, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591167365, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591167737, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591167911, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591168109, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591168514, "dur": 1072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591169587, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591169710, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591170144, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591170547, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591171114, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591171608, "dur": 142225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756862591313885, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1756862591313835, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1756862591314044, "dur": 1979, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1756862591316026, "dur": 62015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591137700, "dur": 14515, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591152226, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_448E988461C74DB0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591152344, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_CEA60A69666C96C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591152442, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_CEA60A69666C96C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591152568, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0DAAC2D6CA6D2720.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591152746, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_7CE7E2E85C9B872F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591152848, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_5BF450534C33B93C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591152951, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_5BF450534C33B93C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591153174, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_0548B6350D93892B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591153362, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_097DDEAFECA28810.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591153533, "dur": 579, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756862591154173, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756862591154416, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591155512, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591156420, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591157284, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591158137, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591159696, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591160503, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591161435, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591162310, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591163249, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591163353, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591163774, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591164537, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591165065, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591165307, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591165365, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756862591166089, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591166323, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591166388, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591166493, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591166582, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591166850, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591167077, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591167213, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756862591167365, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591167430, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756862591167842, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591168180, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591168493, "dur": 1062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591169555, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591169686, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591170125, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591170529, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591171138, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756862591171638, "dur": 206662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591137724, "dur": 14505, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591152239, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_0A94E029953135AC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591152442, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_6A4766F7572387DA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591152519, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_20112DFB1B9A6681.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591152577, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591152641, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_20112DFB1B9A6681.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591152931, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_5A56B4C00496EA36.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591153073, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591153161, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6583F22C0A896B6F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591153254, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6583F22C0A896B6F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591153315, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_3D53C0654B9396F1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591153393, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591153451, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_3D53C0654B9396F1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591153630, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1756862591153946, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1756862591154162, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756862591154326, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756862591154450, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591155509, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591156431, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591157348, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591158204, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591159034, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591160693, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591161757, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591162601, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591163450, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591163787, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591164550, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591165173, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591166039, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591166244, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591166311, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591166473, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591166548, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591166874, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591167079, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591167216, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756862591167403, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756862591167828, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591168071, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591168501, "dur": 1048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591169608, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591169692, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591170166, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591170565, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591171133, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756862591171676, "dur": 206565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591137751, "dur": 14492, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591152253, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4CE88DA2C6FA0DDC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862591152362, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591152466, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_3F0948DC6B40CBC0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862591152927, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_25538968F9D76790.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862591153072, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591153127, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_25538968F9D76790.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862591153303, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_18E07CEE712070C7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862591153413, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_18E07CEE712070C7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862591153946, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1756862591154055, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1756862591154327, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1756862591154387, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591155736, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591156624, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591157542, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591158393, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591159598, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591160846, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591161973, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591162893, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591163031, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591163131, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591163383, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591163775, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591164535, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591165030, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591165081, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862591165382, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756862591166205, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591166375, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591166759, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862591166911, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756862591167439, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591167769, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591167923, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591168050, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591168498, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591169551, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591169688, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591170123, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591170527, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591170612, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756862591170732, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756862591170981, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591171152, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756862591171620, "dur": 206414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591138326, "dur": 14182, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591152509, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_4297D6ED2C7F7138.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756862591152659, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591152748, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2731044146C71882.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756862591152975, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A067B7A9CA1EAF47.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756862591153171, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A067B7A9CA1EAF47.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756862591153419, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756862591153636, "dur": 436, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756862591154269, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1756862591154390, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591155536, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591156408, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591157507, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591158351, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591159510, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591161058, "dur": 631, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@be6c4fd0abf5\\InputSystem\\Editor\\Analytics\\PlayerInputEditorAnalytic.cs"}}, {"pid": 12345, "tid": 8, "ts": 1756862591160403, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591161858, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591162759, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591163587, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591163800, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591164560, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591165121, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756862591165395, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756862591165955, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591166327, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756862591166390, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591166494, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591166571, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591166869, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591167099, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591167304, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591167715, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591167889, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591168119, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591168517, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591169575, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591169694, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591170173, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591170578, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591171118, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756862591171756, "dur": 206323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591137822, "dur": 14450, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591152283, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_470099C400F771FA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591152400, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_81ADD57237B7A215.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591152454, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591152603, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_81ADD57237B7A215.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591152785, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5706F0A31B0C16B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591152893, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5706F0A31B0C16B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591153030, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_57A4CF732EF2CD29.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591153205, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_57A4CF732EF2CD29.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591153310, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_56F11C16920C2AB4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591153528, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591153579, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756862591153916, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591154141, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756862591154344, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591155323, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591156322, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591157740, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591158933, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591160130, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591161168, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591162049, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591163044, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591163134, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591163330, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591163771, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591164525, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591165074, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591165299, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591165355, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591165424, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756862591166084, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591166275, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591166380, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756862591166598, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756862591167023, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591167275, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591167708, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591167887, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591167940, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591168098, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591168510, "dur": 1067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591169577, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591169703, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591170138, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591170539, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591171123, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756862591171733, "dur": 206368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591137865, "dur": 14421, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591152299, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862591152375, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591152426, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862591152499, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_6B03EF9A2B2BFBD3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862591152656, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591152706, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_6B03EF9A2B2BFBD3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862591152851, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D52594234CD03E41.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862591152924, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591153060, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_20E9E08310ABADB8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862591153172, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_43979216E7A92FE9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862591153442, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862591153732, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591153847, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756862591153906, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591153998, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1756862591154137, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756862591154236, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591154339, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1756862591154551, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591155624, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591156499, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591157474, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591158341, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591159517, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591160588, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591161452, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591162331, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591163142, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591163374, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591163770, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591164578, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591165075, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756862591165388, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756862591165990, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591166203, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591166313, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591166472, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591166546, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591166823, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591167276, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591167698, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591167752, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591167918, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591168080, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591168506, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591169572, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591169696, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591170131, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591170537, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591171120, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756862591171740, "dur": 206347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591137893, "dur": 14412, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591152316, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_88B5229BB905F051.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862591152463, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_943D1566EB36197C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862591152635, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_943D1566EB36197C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862591152787, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D443F0926FAEB623.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862591152840, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591152924, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862591153060, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591153182, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3FD29ACDA5717940.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862591153267, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3FD29ACDA5717940.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862591153498, "dur": 602, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1756862591154113, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591154164, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756862591154465, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591155506, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591156488, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591157469, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591158684, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591160102, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591160965, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591161913, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591162845, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591163493, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591163784, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591164540, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591165211, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591166037, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591166201, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591166489, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591166566, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591166897, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756862591167071, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756862591167470, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591167747, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591167916, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591168099, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591168512, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591169570, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591169736, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756862591169830, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591170128, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591170568, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591171116, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591171606, "dur": 10204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591183202, "dur": 213, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 11, "ts": 1756862591181810, "dur": 1611, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756862591183421, "dur": 194645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591137922, "dur": 14397, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591152330, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7E5B9059DDF5C58D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862591152416, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591152535, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1C4A8B1B6898FD4D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862591152643, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1C4A8B1B6898FD4D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862591152944, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_886CDD09B49D7AF9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862591153128, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_886CDD09B49D7AF9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862591153323, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862591153412, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862591153826, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756862591153904, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591154066, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756862591154294, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1756862591154356, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591155644, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591156499, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591157566, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591158429, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591159696, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591160575, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591161448, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591162321, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591163436, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591163817, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591164572, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591165073, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756862591165372, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756862591165874, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591166038, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591166098, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591166393, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591166541, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591166822, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591166891, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591167101, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591167294, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591167707, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591167889, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591168043, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591168093, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591168522, "dur": 1076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591169598, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591169721, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591170154, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591170554, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591171119, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756862591171748, "dur": 206420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591137962, "dur": 14372, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591152344, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BB73E5A042403DAF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862591152447, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BB73E5A042403DAF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862591152553, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_94137FF513C48ABF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862591152644, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_94137FF513C48ABF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862591152908, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591152968, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_6EEC5CB3D74D8DC0.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862591153327, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_35E2FC16174B6AD9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756862591153395, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591153510, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1756862591153824, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756862591153965, "dur": 652, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1756862591154618, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591155811, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591157036, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591157892, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591158750, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591160020, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591160884, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591161778, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591162639, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591163447, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591163787, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591164548, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591165183, "dur": 847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591166030, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591166153, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591166206, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591166303, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591166369, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591166565, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591166906, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591167092, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591167317, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591167717, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591167896, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591168073, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591168499, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591169604, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591169729, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591170168, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591170577, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591171129, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756862591171700, "dur": 206484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591137993, "dur": 14359, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591152363, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_05BE74699551E17D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862591152450, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591152562, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_DA0ED1A2C90566BA.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862591152672, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_798AE90902F00157.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862591152723, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591152849, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591152919, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_558CAE984AE1D1A5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862591153141, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591153345, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862591153420, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591153537, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756862591153821, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756862591153940, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756862591154137, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756862591154262, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591154421, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591155584, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591156488, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591157531, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591158431, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591159731, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591160643, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591161525, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591162433, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591163265, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591163356, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591163768, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591164526, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591165078, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862591165352, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862591165431, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756862591165965, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591166162, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591166310, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756862591166487, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756862591166932, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591167124, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591167323, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591167724, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591167907, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591168145, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591168528, "dur": 1074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591169602, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591169725, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591170156, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591170572, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591171157, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756862591171612, "dur": 206424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591138029, "dur": 14342, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591152384, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B40F3C4651EACB98.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862591152460, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591152524, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C124AD4B97AA3982.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862591152907, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B76ACB483990A7B3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862591152959, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591153144, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_076091C3DFC2A810.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862591153204, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591153255, "dur": 383, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_076091C3DFC2A810.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756862591153830, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756862591153938, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756862591154049, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1756862591154196, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756862591154301, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591154351, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756862591154431, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591155583, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591156643, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591157684, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591158572, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591159884, "dur": 1168, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@be6c4fd0abf5\\InputSystem\\Plugins\\UI\\InputSystemUIInputModuleEditor.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756862591161678, "dur": 752, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@be6c4fd0abf5\\InputSystem\\Plugins\\PlayerInput\\PlayerNotifications.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756862591159884, "dur": 2715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591162599, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591163565, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591163797, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591164556, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591165138, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591166040, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591166147, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591166205, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591166292, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591166373, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591166434, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591166488, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591166563, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591166917, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591167089, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591167344, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591167730, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591167903, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591167992, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591168047, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591168524, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591169584, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591169706, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591170142, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591170544, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591171133, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756862591171684, "dur": 206530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591138073, "dur": 14315, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591152400, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_30460E0C3839B364.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862591152688, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_574DE5121CC9D879.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862591152745, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591152808, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_21F715593E655655.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862591152890, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591152948, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_21F715593E655655.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862591153167, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AFDD633454108E81.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862591153263, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591153318, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1756862591153436, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591153503, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591153562, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591153625, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756862591153732, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756862591153998, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1756862591154272, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756862591154360, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591155346, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591156210, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591157099, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591157964, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591158795, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591160141, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591160974, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591161969, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591162796, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591163544, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591163803, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591164565, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591165112, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756862591165406, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756862591166099, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591166482, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591166568, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591166878, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591167111, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591167282, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591167707, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591167787, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591168131, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591168520, "dur": 1061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591169582, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591169705, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591170140, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591170542, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591171113, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591171607, "dur": 11821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756862591183429, "dur": 194625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591138110, "dur": 14293, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591152413, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_C2B2B5EFE7050494.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862591152602, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_C2B2B5EFE7050494.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862591152735, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D69F050D9A0ED123.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862591152846, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_080CB5314CBF4724.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862591152938, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591153080, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BA4F652EDE8ADBA9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862591153196, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BA4F652EDE8ADBA9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862591153304, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862591153551, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591153838, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756862591154002, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1756862591154191, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756862591154394, "dur": 409, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756862591154804, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591155866, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591157678, "dur": 700, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\LudiqEditorWindow.cs"}}, {"pid": 12345, "tid": 17, "ts": 1756862591158470, "dur": 1095, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\ICanvasWindow.cs"}}, {"pid": 12345, "tid": 17, "ts": 1756862591156679, "dur": 2979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591159658, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591160517, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591161462, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591162335, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591163271, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591163336, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591163810, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591164571, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591165083, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862591165412, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756862591166228, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591166545, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756862591166714, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756862591167706, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591168164, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591168529, "dur": 1066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591169596, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591169718, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591170145, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591170564, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591171150, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756862591171629, "dur": 206403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591138139, "dur": 14277, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591152425, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E4EBE1DEA4B9DBF9.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862591152573, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591152638, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E4EBE1DEA4B9DBF9.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862591152693, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_324EC462D42B6AD5.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862591152795, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_324EC462D42B6AD5.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862591153063, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_128BE6A580A94DC6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862591153291, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EF1B9BDC36CA0637.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862591153385, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EF1B9BDC36CA0637.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756862591153508, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591153577, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591153634, "dur": 300, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756862591153936, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591154153, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756862591154250, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591154301, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756862591154477, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591155517, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591156382, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591157246, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591158086, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591158903, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591160161, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591161046, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591161925, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591162777, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591163567, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591163796, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591164554, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591165151, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591166021, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591166148, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591166292, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591166421, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591166475, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591166558, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591166900, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591167084, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591167197, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591167298, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591167710, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591167891, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591168044, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591168487, "dur": 1066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591169554, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591169686, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591170123, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591170528, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591171132, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756862591171692, "dur": 206536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591138184, "dur": 14245, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591152441, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_B9DC0DB8A04DAC4C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756862591152610, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_B9DC0DB8A04DAC4C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756862591152903, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B9618BB877B2AF9C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756862591153016, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8065517B5A536D03.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756862591153472, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756862591153624, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756862591153844, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591153947, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1756862591154199, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756862591154355, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756862591154590, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591156113, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591157294, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591158135, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591158963, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591160169, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591161039, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591161919, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591162627, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591163415, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591163777, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591164539, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591165221, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591166029, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591166205, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591166429, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591166480, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591166551, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591166937, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591167091, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591167332, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591167725, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591167908, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591168135, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591168525, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591169590, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591169722, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591170164, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591170563, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591171135, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756862591171657, "dur": 206596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591138212, "dur": 14234, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591152460, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EA0B5218CB807CE8.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591152758, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_AF9E01432509B98A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591152882, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D2ABA6F656B34D47.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591152936, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591153178, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F7EA645C15EDC022.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591153229, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591153397, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756862591153492, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591153552, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591153615, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756862591153686, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591153742, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756862591153897, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591153949, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1756862591154158, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756862591154300, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591154350, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756862591154413, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591155461, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591156497, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591157555, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591158558, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591159752, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591160606, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591161471, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591162377, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591163193, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591163350, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591163799, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591164559, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591165127, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591166020, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591166244, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591166327, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591166394, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591166470, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591166589, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591166840, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591167116, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591167375, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591167698, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591167805, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591167892, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591167994, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591168045, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591168486, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591168565, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591168701, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756862591169542, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591169761, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591169838, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591169981, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756862591170411, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591170607, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591170709, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756862591171012, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591171146, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591171251, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756862591171493, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756862591171641, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756862591171752, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756862591172591, "dur": 136612, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756862591313849, "dur": 62955, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756862591313829, "dur": 62977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756862591376828, "dur": 1131, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 21, "ts": 1756862591138235, "dur": 14224, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591152471, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_B16CC6C88F056D62.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591152645, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_B16CC6C88F056D62.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591152755, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_40502E6DCAE34EBD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591152833, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591152894, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_47FD15CC38401525.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591153122, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D8EA2BB5196147D6.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591153199, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591153255, "dur": 785, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D8EA2BB5196147D6.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591154049, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591154224, "dur": 9426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756862591163651, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591163816, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591163923, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756862591164415, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591164596, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591164687, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756862591164923, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591165064, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591165361, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756862591166744, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591167216, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591167416, "dur": 941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756862591168358, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591168559, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591168681, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756862591169437, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591169620, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756862591169727, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756862591170005, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591170182, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591170531, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591171134, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756862591171666, "dur": 206537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591138258, "dur": 14218, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591152487, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_12BCE5693E524B55.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862591152633, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591152694, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5A472186080A5AB1.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862591152749, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591152817, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5A472186080A5AB1.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862591152940, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6941CE050E1CF5A2.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862591153110, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591153161, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6941CE050E1CF5A2.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862591153437, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1756862591153708, "dur": 826, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1756862591154535, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591155504, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591157144, "dur": 669, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Graph\\HasGraph.cs"}}, {"pid": 12345, "tid": 22, "ts": 1756862591156431, "dur": 1897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591158328, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591159737, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591160609, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591161554, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591162462, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591163340, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591163818, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591164533, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591165029, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591165084, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862591165348, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862591165410, "dur": 1293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756862591166704, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591166827, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591166883, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756862591167079, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756862591167642, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591167802, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591167925, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591168020, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591168155, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591168532, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591169592, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591169712, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591170171, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591170570, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591171126, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756862591171716, "dur": 206422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591138290, "dur": 14203, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591152503, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_51DEFFE41CB8BCA4.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862591152767, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3A6F5DE8BB04652D.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862591152868, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EFBDB924F7F1B25C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862591152919, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591153129, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F9E2212BE69EFFE3.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862591153239, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_47D5F37EEAD56BE0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862591153436, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756862591153752, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1756862591154104, "dur": 415, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756862591154520, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591155567, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591156433, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591158543, "dur": 1269, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\BoltStyles.cs"}}, {"pid": 12345, "tid": 23, "ts": 1756862591157995, "dur": 2312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591160307, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591161159, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591162036, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591162860, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591163186, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591163332, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591163768, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591164569, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591165077, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862591165296, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591165371, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756862591165885, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591166486, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862591166681, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756862591167152, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591167319, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756862591167495, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756862591167934, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591168075, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591168502, "dur": 1063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591169566, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591169693, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591170132, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591170570, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591171117, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756862591171656, "dur": 206617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591137782, "dur": 14475, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591152269, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3352A694B6772687.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862591152348, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591152456, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591152647, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EB9810B219936736.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862591152710, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591152767, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EB9810B219936736.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862591152994, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_3AD12250A59F4CEE.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862591153307, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_CBCEC9956E2477BD.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862591153549, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591153723, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1756862591153895, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1756862591154050, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862591154227, "dur": 8939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1756862591163167, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591163402, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591163780, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591164542, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591165202, "dur": 843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591166045, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591166200, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591166290, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591166426, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591166478, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591166546, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591166871, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591167138, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591167227, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756862591167423, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591167530, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1756862591167988, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591168110, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591168516, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591169576, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591169699, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591170135, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591170549, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591171125, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756862591171724, "dur": 206396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756862591383398, "dur": 1675, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 45648, "tid": 394, "ts": 1756862591386135, "dur": 4718, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 45648, "tid": 394, "ts": 1756862591390913, "dur": 747, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 45648, "tid": 394, "ts": 1756862591385358, "dur": 6328, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}