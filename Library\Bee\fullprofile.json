{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 45648, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 45648, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 45648, "tid": 517, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 45648, "tid": 517, "ts": 1756863488140413, "dur": 493, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 45648, "tid": 517, "ts": 1756863488144102, "dur": 664, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 45648, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 45648, "tid": 1, "ts": 1756863487888379, "dur": 4013, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 45648, "tid": 1, "ts": 1756863487892395, "dur": 38543, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 45648, "tid": 1, "ts": 1756863487930946, "dur": 24658, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 45648, "tid": 517, "ts": 1756863488144770, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 45648, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487886838, "dur": 6021, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487892860, "dur": 240857, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487893860, "dur": 2364, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487896228, "dur": 1353, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897583, "dur": 132, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897718, "dur": 9, "ph": "X", "name": "ProcessMessages 20508", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897728, "dur": 24, "ph": "X", "name": "ReadAsync 20508", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897754, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897774, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897798, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897821, "dur": 38, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897862, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897863, "dur": 37, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897901, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897903, "dur": 23, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487897928, "dur": 69, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898002, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898034, "dur": 30, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898067, "dur": 19, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898087, "dur": 19, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898110, "dur": 19, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898131, "dur": 28, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898160, "dur": 18, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898180, "dur": 20, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898203, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898225, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898247, "dur": 19, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898268, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898291, "dur": 20, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898313, "dur": 14, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898329, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898348, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898350, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898369, "dur": 19, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898390, "dur": 25, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898418, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898420, "dur": 50, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898477, "dur": 3, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898482, "dur": 58, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898542, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898544, "dur": 29, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898576, "dur": 29, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898609, "dur": 28, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898639, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898640, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898664, "dur": 18, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898685, "dur": 19, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898706, "dur": 20, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898728, "dur": 28, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898759, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898782, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898806, "dur": 18, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898827, "dur": 23, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898852, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898876, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898879, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898906, "dur": 27, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898936, "dur": 26, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898965, "dur": 17, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487898985, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899006, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899028, "dur": 1, "ph": "X", "name": "ProcessMessages 158", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899030, "dur": 35, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899068, "dur": 17, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899087, "dur": 18, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899108, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899126, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899146, "dur": 16, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899163, "dur": 18, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899184, "dur": 27, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899213, "dur": 21, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899236, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899257, "dur": 17, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899277, "dur": 15, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899294, "dur": 22, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899318, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899337, "dur": 18, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899356, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899358, "dur": 18, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899378, "dur": 20, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899402, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899425, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899446, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899467, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899488, "dur": 17, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899507, "dur": 20, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899529, "dur": 16, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899547, "dur": 17, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899567, "dur": 23, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899591, "dur": 20, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899613, "dur": 18, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899633, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899653, "dur": 18, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899675, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899699, "dur": 22, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899724, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899747, "dur": 20, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899770, "dur": 23, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899795, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899797, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899821, "dur": 19, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899841, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899863, "dur": 24, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899889, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899910, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899912, "dur": 19, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899934, "dur": 17, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899952, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487899976, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900001, "dur": 26, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900029, "dur": 15, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900046, "dur": 23, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900073, "dur": 30, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900105, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900126, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900148, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900175, "dur": 29, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900206, "dur": 19, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900227, "dur": 26, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900256, "dur": 20, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900278, "dur": 133, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900413, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900446, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900479, "dur": 19, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900500, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900526, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900550, "dur": 16, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900570, "dur": 16, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900588, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900606, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900608, "dur": 23, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900633, "dur": 18, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900653, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900673, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900694, "dur": 24, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900720, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900743, "dur": 18, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900764, "dur": 17, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900783, "dur": 18, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900803, "dur": 21, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900826, "dur": 17, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900845, "dur": 18, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900864, "dur": 31, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900899, "dur": 26, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900927, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900948, "dur": 20, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900970, "dur": 18, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487900991, "dur": 21, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901014, "dur": 29, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901045, "dur": 19, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901067, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901092, "dur": 27, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901121, "dur": 20, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901142, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901144, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901165, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901185, "dur": 17, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901205, "dur": 16, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901223, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901244, "dur": 17, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901263, "dur": 17, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901282, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901303, "dur": 34, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901340, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901358, "dur": 17, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901378, "dur": 18, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901398, "dur": 14, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901414, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901437, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901459, "dur": 26, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901487, "dur": 19, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901508, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901529, "dur": 24, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901556, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901583, "dur": 20, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901606, "dur": 22, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901631, "dur": 28, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901661, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901681, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901706, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901732, "dur": 24, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901759, "dur": 19, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901780, "dur": 23, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901806, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901807, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901832, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901853, "dur": 20, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901876, "dur": 19, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901899, "dur": 31, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901932, "dur": 19, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901953, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901973, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487901994, "dur": 18, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902014, "dur": 17, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902033, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902054, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902074, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902092, "dur": 18, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902112, "dur": 37, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902152, "dur": 24, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902180, "dur": 28, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902211, "dur": 18, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902231, "dur": 16, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902249, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902271, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902290, "dur": 17, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902310, "dur": 18, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902330, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902349, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902371, "dur": 24, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902398, "dur": 18, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902418, "dur": 25, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902445, "dur": 18, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902465, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902488, "dur": 17, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902508, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902538, "dur": 19, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902559, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902580, "dur": 21, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902603, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902629, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902654, "dur": 25, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902681, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902701, "dur": 20, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902723, "dur": 19, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902744, "dur": 19, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902766, "dur": 20, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902788, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902810, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902831, "dur": 19, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902853, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902875, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902896, "dur": 21, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902919, "dur": 19, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902940, "dur": 19, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902960, "dur": 18, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487902980, "dur": 18, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903001, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903022, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903043, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903066, "dur": 18, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903087, "dur": 25, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903114, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903116, "dur": 26, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903144, "dur": 18, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903164, "dur": 22, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903188, "dur": 17, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903207, "dur": 17, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903226, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903246, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903267, "dur": 18, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903287, "dur": 17, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903307, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903327, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903345, "dur": 22, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903369, "dur": 26, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903397, "dur": 19, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903418, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903439, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903459, "dur": 13, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903474, "dur": 16, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903492, "dur": 18, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903513, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903534, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903536, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903558, "dur": 19, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903579, "dur": 17, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903599, "dur": 17, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903618, "dur": 17, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903637, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903657, "dur": 17, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903677, "dur": 81, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903762, "dur": 33, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903797, "dur": 1, "ph": "X", "name": "ProcessMessages 1951", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903798, "dur": 24, "ph": "X", "name": "ReadAsync 1951", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903824, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903846, "dur": 31, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903880, "dur": 17, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903910, "dur": 26, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903938, "dur": 18, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903959, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903978, "dur": 18, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487903998, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904018, "dur": 20, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904039, "dur": 18, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904060, "dur": 17, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904079, "dur": 18, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904098, "dur": 19, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904119, "dur": 26, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904148, "dur": 21, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904170, "dur": 20, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904193, "dur": 18, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904214, "dur": 19, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904236, "dur": 18, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904257, "dur": 17, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904276, "dur": 17, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904296, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904320, "dur": 26, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904349, "dur": 271, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904625, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904686, "dur": 291, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487904980, "dur": 83, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905070, "dur": 6, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905077, "dur": 52, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905132, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905135, "dur": 38, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905176, "dur": 41, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905220, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905222, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905259, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905261, "dur": 58, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905322, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905325, "dur": 47, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905375, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905377, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905427, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905429, "dur": 44, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905476, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905478, "dur": 48, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905529, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905532, "dur": 48, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905582, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905583, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905632, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905634, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905687, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905690, "dur": 55, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905749, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905752, "dur": 45, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905801, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905804, "dur": 51, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905859, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905862, "dur": 61, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905927, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905930, "dur": 60, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905993, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487905996, "dur": 49, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906048, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906051, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906105, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906109, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906155, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906160, "dur": 51, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906215, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906218, "dur": 47, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906269, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906272, "dur": 47, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906322, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906325, "dur": 476, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906805, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906808, "dur": 59, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906872, "dur": 4, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906878, "dur": 80, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487906964, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487907018, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487907021, "dur": 9280, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916307, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916345, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916348, "dur": 235, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916589, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916630, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916635, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916686, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916716, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487916718, "dur": 452, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487917252, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487917486, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487917489, "dur": 58, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487917592, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487917595, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487917835, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487917905, "dur": 338, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487918314, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487918316, "dur": 190, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487918531, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487918533, "dur": 188, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487918780, "dur": 3, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487918784, "dur": 189, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487919041, "dur": 42, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487919085, "dur": 200, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487921940, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487921944, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922002, "dur": 5, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922008, "dur": 85, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922099, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922144, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922194, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922233, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922234, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922282, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922287, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922354, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922358, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922389, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922391, "dur": 72, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922467, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922490, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922492, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922524, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922529, "dur": 281, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922814, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922859, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922861, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922914, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922952, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487922955, "dur": 69, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923028, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923055, "dur": 247, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923306, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923344, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923347, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923390, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923393, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923428, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923494, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923524, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487923529, "dur": 975, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487924509, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487924538, "dur": 482, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863487925023, "dur": 156454, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488081488, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488081493, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488081569, "dur": 1567, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488083139, "dur": 3381, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488086524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488086526, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488086571, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488086574, "dur": 2069, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488088649, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488088651, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488088688, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488088703, "dur": 36121, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488124831, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488124836, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488124883, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488124887, "dur": 995, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488125886, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488125924, "dur": 18, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488125943, "dur": 780, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488126728, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488126755, "dur": 431, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756863488127189, "dur": 5948, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 45648, "tid": 517, "ts": 1756863488144779, "dur": 548, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 45648, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 45648, "tid": 8589934592, "ts": 1756863487884717, "dur": 70961, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 45648, "tid": 8589934592, "ts": 1756863487955683, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 45648, "tid": 8589934592, "ts": 1756863487955690, "dur": 5164, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 45648, "tid": 517, "ts": 1756863488145329, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 45648, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 45648, "tid": 4294967296, "ts": 1756863487868562, "dur": 266005, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756863487872489, "dur": 6801, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756863488134708, "dur": 3419, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756863488136781, "dur": 79, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756863488138190, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 45648, "tid": 517, "ts": 1756863488145334, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756863487890258, "dur": 1900, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756863487892169, "dur": 386, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756863487892640, "dur": 762, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756863487894410, "dur": 993, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756863487896346, "dur": 1175, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756863487893425, "dur": 10699, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756863487904132, "dur": 221631, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756863488125764, "dur": 224, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756863488125989, "dur": 157, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756863488126172, "dur": 51, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756863488126509, "dur": 1356, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756863487893328, "dur": 10814, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487904157, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_0B128FF1BBAAC47E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756863487904524, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_6B03EF9A2B2BFBD3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756863487904579, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487904642, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EB9810B219936736.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756863487904693, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487904775, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_88DF725418823539.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756863487904868, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D2ABA6F656B34D47.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756863487905013, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487905069, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D2ABA6F656B34D47.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756863487905360, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756863487905457, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756863487905850, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756863487906095, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487906152, "dur": 427, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1756863487906580, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487907707, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487908562, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487909630, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487910575, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487911927, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487912800, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487913825, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487914751, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487915392, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487915660, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487916466, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487916992, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487917125, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756863487917406, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756863487918201, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487918354, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487918566, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487918977, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487919113, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487919366, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487919506, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487919841, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487919909, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487920453, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487921369, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487921475, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487921972, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487922120, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487922682, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756863487923205, "dur": 202587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487893361, "dur": 10796, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487904163, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_253848F44BDD5E17.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756863487904503, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C25CFDCCBD0E71F0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756863487904588, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487904706, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_4F4FD9FF61AF6F28.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756863487904859, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DA76F58CBF310F62.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756863487904983, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487905150, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3FD29ACDA5717940.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756863487905201, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487905260, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3FD29ACDA5717940.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756863487905452, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756863487905771, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487905881, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756863487906031, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756863487906215, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756863487906280, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487907409, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487908392, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487909276, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487910157, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487911565, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487912399, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487913301, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487914181, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487915090, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487915323, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487915772, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487916476, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487917005, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487917138, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487918079, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487918439, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487918981, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487919084, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756863487919344, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756863487919740, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487919934, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487920457, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487921381, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487921466, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487921975, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487922130, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487922673, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756863487923275, "dur": 202530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487893409, "dur": 10759, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487904180, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_71DE750A6D09CA7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756863487904585, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487904669, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_574DE5121CC9D879.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756863487904857, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_EC805490FEC13931.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756863487904993, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487905067, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BA4F652EDE8ADBA9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756863487905355, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_56F11C16920C2AB4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756863487905474, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756863487905752, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756863487905870, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756863487906028, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756863487906283, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487907376, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487908223, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487909098, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487909960, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487910890, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487912163, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487913021, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487913946, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487914852, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487915308, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487915810, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487916427, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487917014, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487917093, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756863487917291, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487917354, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756863487918107, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487918288, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487918360, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756863487918444, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756863487918685, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756863487919193, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487919398, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487919521, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487919864, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487919936, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487920469, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487921392, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487921457, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487921948, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487922142, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487922687, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756863487923220, "dur": 202602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487893435, "dur": 10747, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487904190, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_36F2911304CC020F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756863487904555, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487904712, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_7CE7E2E85C9B872F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756863487904788, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487904875, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E57DD11F6513DD1B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756863487904999, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487905075, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A964C83E710562E0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756863487905348, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_3D53C0654B9396F1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756863487905525, "dur": 394, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756863487906020, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756863487906287, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487907610, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487908502, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487909406, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487910249, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487911553, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487912363, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487913187, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487914108, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487914997, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487915305, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487915684, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487916446, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487916983, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487917123, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487918051, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487918189, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487918294, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487918470, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487918994, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487919237, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487919346, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487919545, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487919916, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487920462, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487921384, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487921462, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487921977, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487922125, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487922670, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756863487923282, "dur": 202536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487893470, "dur": 10722, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487904202, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_448E988461C74DB0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756863487904852, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D52594234CD03E41.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756863487905037, "dur": 446, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D52594234CD03E41.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756863487905490, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487905546, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1756863487905745, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756863487906043, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756863487906261, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487907282, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487908152, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487909118, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487909960, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487910948, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487912211, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487913046, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487913965, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487915065, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487915318, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487915763, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487916460, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487916996, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487917133, "dur": 956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487918090, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487918429, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487918971, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487919031, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487919115, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487919203, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487919312, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487919378, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487919510, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487919838, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487919911, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487920449, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487920510, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756863487920626, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756863487921350, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487921442, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487921941, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487922089, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487922165, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756863487922284, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756863487922536, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487922719, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756863487923168, "dur": 202604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487893508, "dur": 10698, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487904216, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_0A94E029953135AC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756863487904623, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487904699, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5A472186080A5AB1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756863487904836, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5A472186080A5AB1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756863487904933, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_886CDD09B49D7AF9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756863487905140, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_AF814272D3883F57.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756863487905282, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756863487905677, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756863487905896, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756863487906172, "dur": 8954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756863487915127, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487915372, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487915678, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487916433, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487916975, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487917095, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756863487917324, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487917384, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756863487918170, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487918467, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487918992, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487919140, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487919225, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487919359, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487919523, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487919856, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487919929, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487920451, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487921435, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487921934, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487922091, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487922710, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756863487923178, "dur": 202596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487893532, "dur": 10687, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487904230, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4CE88DA2C6FA0DDC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756863487904684, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487904746, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3A6F5DE8BB04652D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756863487904839, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3A6F5DE8BB04652D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756863487905059, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1CF8F8636ADD1698.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756863487905110, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487905170, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1CF8F8636ADD1698.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756863487905246, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EF1B9BDC36CA0637.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756863487905345, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EF1B9BDC36CA0637.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756863487905469, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1756863487905773, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756863487905873, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756863487905972, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1756863487906255, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487907283, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487908262, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487909185, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487910102, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487911027, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487912333, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487913174, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487914119, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487915059, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487915331, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487915740, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487916454, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487916991, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487917132, "dur": 967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487918099, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487918204, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487918270, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487918358, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487918517, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487919005, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487919205, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487919341, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487919500, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487919941, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487920465, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487921390, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487921457, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487921954, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487922103, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487922675, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756863487923267, "dur": 202536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487893570, "dur": 10663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487904242, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3352A694B6772687.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756863487904750, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5706F0A31B0C16B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756863487904862, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EFBDB924F7F1B25C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756863487904984, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487905273, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756863487905403, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756863487905854, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756863487906003, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756863487906127, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487906388, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487907393, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487908640, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487909662, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487910529, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487911806, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487912708, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487913613, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487914570, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487915380, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487915669, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487916470, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487917002, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487917129, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487918101, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487918193, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487918250, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487918329, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487918542, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487919008, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487919158, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487919221, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756863487919422, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487919522, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756863487919943, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487920036, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487920475, "dur": 932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487921436, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487921931, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487922090, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487922663, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487922728, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756863487923148, "dur": 202611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487893603, "dur": 10657, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487904274, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_470099C400F771FA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487904704, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487904780, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_FC8457FE0E2359F5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487904842, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487904898, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_FC8457FE0E2359F5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487905019, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487905094, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8065517B5A536D03.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487905271, "dur": 395, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_47D5F37EEAD56BE0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487905683, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487905844, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487906120, "dur": 9407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756863487915528, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487915698, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487915795, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756863487916310, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487916505, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487916603, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756863487916859, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487917135, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487917398, "dur": 1442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756863487918841, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487919115, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487919377, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756863487920338, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487920505, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487920609, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756863487921268, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487921454, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756863487921568, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756863487921823, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487922009, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487922107, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487922661, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863487923144, "dur": 162987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756863488086177, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1756863488086132, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1756863488086323, "dur": 2141, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1756863488088472, "dur": 37295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487893636, "dur": 10642, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487904287, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756863487904812, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_7D055410D6C309E4.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756863487904886, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_558CAE984AE1D1A5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756863487905200, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487905250, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E38ED73995C25B9E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756863487905348, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756863487905452, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756863487905981, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756863487906251, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487907428, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487908346, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487909277, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487910842, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487912097, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487912979, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487913905, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487914997, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487915296, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487915652, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487916422, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487916963, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487917094, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756863487917311, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487917379, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756863487918176, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487918327, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756863487918425, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487919021, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487919205, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487919358, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487919505, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487919839, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487919952, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487920489, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487921468, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487921968, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487922113, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487922663, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756863487923145, "dur": 202615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487893687, "dur": 10605, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487904304, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_88B5229BB905F051.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487904508, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_51DEFFE41CB8BCA4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487904566, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487904630, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_C223B6F3143702D1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487904740, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_40502E6DCAE34EBD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487904898, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_5A56B4C00496EA36.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487905086, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_5A56B4C00496EA36.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487905375, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487905481, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487905551, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756863487905762, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756863487906036, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756863487906158, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756863487906269, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487907287, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487908158, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487909068, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487909938, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487910932, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487912174, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487913096, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487913995, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487914980, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487915041, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487915322, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487915781, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487916469, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487916997, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487917124, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487918049, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487918189, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487918257, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487918367, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1756863487918477, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487918998, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487919246, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487919333, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487919501, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487919868, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487919938, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487920461, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487921388, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487921455, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487921576, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756863487921981, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487922160, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487922260, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756863487922546, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487922714, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487922818, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756863487923036, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756863487923174, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756863487923280, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756863487924941, "dur": 156281, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756863488086148, "dur": 38409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756863488086127, "dur": 38431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756863488124577, "dur": 1103, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756863487893714, "dur": 10595, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487904319, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7E5B9059DDF5C58D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756863487904430, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E4EBE1DEA4B9DBF9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756863487904481, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487904572, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_20112DFB1B9A6681.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756863487904693, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_324EC462D42B6AD5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756863487904893, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756863487905008, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487905076, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756863487905136, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_2CD26214566796B4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756863487905480, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487905652, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1756863487906039, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756863487906154, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756863487906275, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487907337, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487908232, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487909159, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487910032, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487910913, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487912135, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487912993, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487914054, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487915024, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487915298, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487915361, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487915699, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487916441, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487916976, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487917107, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756863487917348, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756863487918053, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487918245, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756863487918484, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487918560, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756863487919056, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487919234, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487919334, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487919394, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487919518, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487919852, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487919919, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487920491, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487921471, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487921972, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487922115, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487922668, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756863487923290, "dur": 202523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487893746, "dur": 10580, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487904339, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BB73E5A042403DAF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487904460, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0903FEE7BAED8E66.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487904528, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487904586, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_94137FF513C48ABF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487904709, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D69F050D9A0ED123.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487904811, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D69F050D9A0ED123.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487904927, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6941CE050E1CF5A2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487905065, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487905291, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487905460, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756863487906030, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756863487906141, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487906238, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487907322, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487908216, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487909195, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487910079, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487910965, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487912207, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487913036, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487913997, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487914906, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487915034, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487915297, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487915365, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487915687, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487916436, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487916971, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487917089, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487917357, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756863487918140, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487918298, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487918563, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756863487918997, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487919189, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756863487919417, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756863487919715, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487919924, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487920455, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487921378, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487921467, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487921964, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487922109, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487922689, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756863487923186, "dur": 202595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487893783, "dur": 10560, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487904353, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_9520DE7000879E41.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756863487904470, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EA0B5218CB807CE8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756863487904567, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487904686, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_D95410EB09159078.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756863487904741, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487904845, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487904899, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_5BF450534C33B93C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756863487904955, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_3AD12250A59F4CEE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756863487905166, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_3AD12250A59F4CEE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756863487905395, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756863487905543, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756863487905689, "dur": 450, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756863487906141, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487906258, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487906426, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487907677, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487908927, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\EditorBinding\\UnitFooterPortsAttribute.cs"}}, {"pid": 12345, "tid": 14, "ts": 1756863487910692, "dur": 802, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\LogicalExpressionVisitor.cs"}}, {"pid": 12345, "tid": 14, "ts": 1756863487908570, "dur": 2924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487911495, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487912368, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487913217, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487914198, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487914854, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487915086, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487915317, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487915796, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487916473, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487917001, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487917126, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487918050, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487918188, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487918296, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487918361, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1756863487918452, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487918986, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487919082, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756863487919339, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756863487919780, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487919976, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487920473, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487921438, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487921982, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487922131, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487922685, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756863487923212, "dur": 202586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487893810, "dur": 10548, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487904370, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_CEA60A69666C96C6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756863487904488, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_3F0948DC6B40CBC0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756863487904583, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487904681, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_3F0948DC6B40CBC0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756863487904757, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D443F0926FAEB623.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756863487904840, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487904897, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D443F0926FAEB623.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756863487905249, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1756863487905504, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487905559, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756863487905704, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756863487905814, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487905880, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1756863487906144, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487906214, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756863487906307, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487907512, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487908387, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487909256, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487910141, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487911068, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487912249, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487913074, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487913956, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487914975, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487915043, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487915312, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487915788, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487916472, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487917008, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487917143, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487918057, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487918187, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487918258, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487918369, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1756863487918508, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487919006, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487919202, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487919389, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487919519, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487919853, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487919926, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487920472, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487921394, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487921456, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487921949, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487922102, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487922703, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756863487923197, "dur": 202588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487893842, "dur": 10530, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487904379, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_05BE74699551E17D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756863487904483, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_943D1566EB36197C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756863487904714, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2731044146C71882.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756863487904871, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_47FD15CC38401525.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756863487905003, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487905065, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_47FD15CC38401525.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756863487905344, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1756863487905609, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1756863487905756, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756863487905926, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1756863487906039, "dur": 428, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756863487906468, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487907615, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487908506, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487909412, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487910328, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487911690, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487912545, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487913440, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487914368, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487915330, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487915723, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487916451, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487916980, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487917103, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756863487917388, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756863487918057, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487918267, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487918357, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756863487918558, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756863487919371, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487919531, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487919939, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487920490, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487921413, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487921470, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487921966, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487922109, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487922661, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487922725, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756863487923159, "dur": 202597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487893876, "dur": 10509, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487904396, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_6A4766F7572387DA.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756863487904497, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_12BCE5693E524B55.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756863487904573, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487904639, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D83BBE6D171D2E8B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756863487904718, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487904791, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_71130C2744ADE79E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756863487904844, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487904946, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A067B7A9CA1EAF47.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756863487905117, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A067B7A9CA1EAF47.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756863487905426, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756863487905579, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487905678, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1756863487905897, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1756863487906157, "dur": 584, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1756863487907528, "dur": 1970, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage@205a02cbcb39\\Editor\\CoverageWindow\\FolderType.cs"}}, {"pid": 12345, "tid": 17, "ts": 1756863487906742, "dur": 3254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487909997, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487910891, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487912158, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487913012, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487913940, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487914978, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487915036, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487915310, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487915803, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487916489, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487916966, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487917087, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756863487917272, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487917336, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756863487917948, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487918098, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487918214, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487918301, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487918369, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1756863487918504, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487919001, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487919197, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487919384, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487919512, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487919850, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487919922, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487920463, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487921386, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487921460, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487921960, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487922114, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487922664, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487923142, "dur": 37497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487962101, "dur": 218, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 17, "ts": 1756863487960640, "dur": 1686, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756863487962326, "dur": 163482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487893920, "dur": 10480, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487904409, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B40F3C4651EACB98.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756863487904480, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487904581, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1C4A8B1B6898FD4D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756863487904695, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487904745, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1C4A8B1B6898FD4D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756863487904944, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_6EEC5CB3D74D8DC0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756863487905069, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487905143, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_6EEC5CB3D74D8DC0.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756863487905248, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487905298, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756863487905576, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756863487905677, "dur": 607, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756863487906285, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487907446, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.State\\Transitions\\FlowStateTransitionWidget.cs"}}, {"pid": 12345, "tid": 18, "ts": 1756863487907388, "dur": 1745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487909134, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487910140, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487911039, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487912247, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487913180, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487914150, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487915122, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487915354, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487915708, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487916455, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487916985, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487917122, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756863487917364, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756863487918144, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487918363, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487918421, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487918972, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487919085, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756863487919333, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756863487919680, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487919862, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487919932, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487920454, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487921459, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487921958, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487922117, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487922667, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756863487923177, "dur": 202613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487893953, "dur": 10460, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487904423, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_81ADD57237B7A215.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756863487904489, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487904577, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C124AD4B97AA3982.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756863487904672, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487904879, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B76ACB483990A7B3.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756863487904999, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487905109, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487905198, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AFDD633454108E81.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756863487905271, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756863487905487, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1756863487905560, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756863487905646, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487905778, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487905935, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1756863487906302, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487907502, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487908369, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487909257, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487910124, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487911011, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487912212, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487913049, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487913968, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487914925, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487914988, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487915298, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487915653, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487916421, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487916964, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487917090, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756863487917340, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756863487918085, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487918322, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756863487918435, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756863487918640, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756863487919152, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487919316, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487919381, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487919525, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487919946, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487920470, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487921396, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487921448, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487921961, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487922104, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487922681, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756863487923229, "dur": 202595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487893986, "dur": 10441, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487904438, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_B03107865E80E25E.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756863487904604, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487904713, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487904905, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F270133E58124F18.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756863487905021, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487905089, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F270133E58124F18.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756863487905259, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1756863487905516, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487905747, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756863487906015, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756863487906149, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756863487906256, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487907394, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487908326, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487909263, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487910128, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487911684, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487912525, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487913380, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487914322, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487915202, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487915333, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487915731, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487916451, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487916987, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487917113, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756863487917368, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756863487918210, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487918368, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756863487918488, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487919015, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487919120, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487919204, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487919353, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487919516, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487919908, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487920049, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487920477, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487921447, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487921948, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487922097, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487922663, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487923143, "dur": 39189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756863487962333, "dur": 163483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487894023, "dur": 10417, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487904450, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B3B4C94707F7A218.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756863487904502, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487904591, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_DA0ED1A2C90566BA.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756863487904702, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_47278636E8915295.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756863487904773, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487904823, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_47278636E8915295.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756863487904996, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F9E2212BE69EFFE3.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756863487905062, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487905121, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6583F22C0A896B6F.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756863487905175, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487905307, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756863487905758, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756863487905850, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1756863487906012, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756863487906236, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487907381, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487908243, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487909163, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487910040, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487910925, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487912124, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487912988, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487913895, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487915095, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487915326, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487915753, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487916465, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487916990, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487917118, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756863487917381, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756863487918270, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487918459, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487919000, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487919216, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487919352, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487919547, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487919832, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487919910, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487920495, "dur": 879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487921375, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487921472, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487921973, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487922124, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487922676, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756863487923257, "dur": 202554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487894065, "dur": 10395, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487904472, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_B9DC0DB8A04DAC4C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756863487904584, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487904659, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_798AE90902F00157.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756863487904895, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_25538968F9D76790.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756863487905013, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487905199, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_88D8DDE2BF366D61.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756863487905382, "dur": 529, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_097DDEAFECA28810.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756863487906061, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1756863487906223, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487907619, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487908487, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487909398, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487910282, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487911656, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487912667, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487913555, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487914181, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487915182, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487915344, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487915716, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487916447, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487916984, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487917113, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756863487917331, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487917394, "dur": 1621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756863487919015, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487919213, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756863487919451, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756863487919767, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487919949, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487920483, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487921438, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487921978, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487922128, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487922677, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756863487923246, "dur": 202560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487894092, "dur": 10384, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487904486, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_B16CC6C88F056D62.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756863487904607, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0DAAC2D6CA6D2720.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756863487904719, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_80D3DD5FCF7A4155.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756863487904816, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_80D3DD5FCF7A4155.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756863487905014, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_20E9E08310ABADB8.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756863487905202, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_43979216E7A92FE9.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756863487905491, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487905699, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1756863487905785, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756863487905935, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1756863487906230, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487907194, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487908514, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487909411, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487910382, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487911660, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487912516, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487913412, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487914299, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487915238, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487915304, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487915692, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487916439, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487917005, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487917141, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487918068, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487918189, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487918363, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1756863487918461, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487918988, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487919152, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487919227, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487919351, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487919498, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487919855, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487919928, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487920480, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487921444, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487921945, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487922136, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487922680, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756863487923237, "dur": 202575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487894126, "dur": 10368, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487904495, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_3BECFE23642E1E2A.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756863487904576, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487904653, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_AF9E01432509B98A.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756863487904718, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487904786, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B9618BB877B2AF9C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756863487904867, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487904993, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756863487905138, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487905196, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_076091C3DFC2A810.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756863487905392, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1756863487905706, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487905900, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1756863487906065, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756863487906246, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487907558, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487908434, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487909346, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487910225, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487911548, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487912394, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487913275, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487914154, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487915111, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487915329, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487915747, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487916457, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487916998, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487917127, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487918056, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487918191, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487918355, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487918423, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487918971, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487919115, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487919199, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487919331, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487919499, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487919915, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487920449, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487921370, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487921476, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487921936, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487922090, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487922665, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756863487923195, "dur": 202601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756863488130913, "dur": 1560, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 45648, "tid": 517, "ts": 1756863488146755, "dur": 1924, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 45648, "tid": 517, "ts": 1756863488148728, "dur": 1789, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 45648, "tid": 517, "ts": 1756863488142975, "dur": 8433, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}